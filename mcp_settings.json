{"mcpServers": {"amap": {"command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "your-api-key"}, "enabled": true}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest", "--headless"], "tools": {"playwright-browser_close": {"enabled": true}}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {}}, "dingtalk-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "dingtalk-mcp@latest"], "env": {"DINGTALK_CLIENT_ID": "dingg9uhwt4zxgf3xss5", "DINGTALK_CLIENT_SECRET": "GvUCEr_nT3RWbsG3aDQ9FxdGoXRQg285SiH-ahOAkm4oJSq4sOKm0DrodYYWdF2J\"", "ACTIVE_PROFILES": "ALL"}}}, "users": [{"username": "admin", "password": "$2b$10$Vt7krIvjNgyN67LXqly0uOcTpN0LI55cYRbcKC71pUDAP0nJ7RPa.", "isAdmin": true}, {"username": "testuser", "password": "$2b$10$hJARqnZMRg8iOGOPIKCAXuQJM5.vRXE5IJhvmLMRLjwmPIbLpiZce", "isAdmin": false}], "groups": [{"id": "42e4acd3-ad37-4593-8eb5-caa3cb8bc1be", "name": "test", "description": "", "servers": ["amap", "fetch"], "bearerAuthKey": "d_i8mem3TZ6_e35.5vm.NkTCTNgpXrC_FxsuhR8MpkSs6pPi"}, {"id": "8aa7604a-875e-4b23-a19e-1e6d2139f8be", "name": "auth1", "description": "", "servers": ["context7", "sequential-thinking", "playwright", "fetch"]}, {"id": "34c975e5-9469-4f1d-ba65-b75858fedb45", "name": "aaa", "description": "", "servers": ["sequential-thinking"]}], "systemConfig": {"routing": {"enableGlobalRoute": true, "enableGroupNameRoute": true, "enableBearerAuth": false, "bearerAuthKey": "gDoBeUicGw5Tq4YBAIUpZ6MI9riSDBNR", "skipAuth": true}, "install": {"pythonIndexUrl": "", "npmRegistry": ""}, "smartRouting": {"enabled": false, "dbUrl": "*******************************************/mcphub", "openaiApiBaseUrl": "", "openaiApiKey": "", "openaiApiEmbeddingModel": ""}}}